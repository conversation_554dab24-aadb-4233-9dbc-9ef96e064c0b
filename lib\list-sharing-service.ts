import { db } from './firebase-admin';
import { 
  CompartilhamentoPublicoLista,
  ConviteCompartilhamentoLista,
  ColaboradorLista,
  SolicitacaoCompartilhamentoLista
} from '@/types/list';

/**
 * 🔗 SERVIÇO DE COMPARTILHAMENTO DE LISTAS
 * Gerencia compartilhamento público, convites e colaboradores
 */
export class ListSharingService {
  private static SHARING_COLLECTION = 'list_sharing';
  private static INVITES_COLLECTION = 'list_invites';
  private static COLLABORATORS_COLLECTION = 'list_collaborators';

  /**
   * 🔗 Gerar link público para lista
   */
  static async gerarLinkPublico(request: {
    listaId: string;
    listaNome: string;
    criadoPor: string;
    criadoPorNome: string;
    configuracoes?: {
      permitirDownload?: boolean;
      permitirComentarios?: boolean;
      mostrarEstatisticas?: boolean;
      senhaProtegido?: string;
      dataExpiracao?: Date;
    };
  }): Promise<{ token: string; linkCompartilhamento: string }> {
    try {
      // Verificar se já existe um compartilhamento ativo para esta lista
      const compartilhamentoExistente = await db.collection(this.SHARING_COLLECTION)
        .where('listaId', '==', request.listaId)
        .where('status', '==', 'ativo')
        .get();

      // Se já existe, retornar o existente
      if (!compartilhamentoExistente.empty) {
        const existente = compartilhamentoExistente.docs[0].data() as CompartilhamentoPublicoLista;
        return {
          token: existente.token,
          linkCompartilhamento: `/shared/${existente.token}/list/public`
        };
      }

      // Gerar token único
      const token = this.gerarToken();
      
      // Preparar configurações sem valores undefined
      const configuracoes: any = {
        permitirDownload: request.configuracoes?.permitirDownload ?? true,
        permitirComentarios: request.configuracoes?.permitirComentarios ?? false,
        mostrarEstatisticas: request.configuracoes?.mostrarEstatisticas ?? false
      };

      // Adicionar senha apenas se fornecida
      if (request.configuracoes?.senhaProtegido) {
        configuracoes.senhaProtegido = request.configuracoes.senhaProtegido;
      }

      const compartilhamento: any = {
        listaId: request.listaId,
        listaNome: request.listaNome,
        criadoPor: request.criadoPor,
        criadoPorNome: request.criadoPorNome,
        token,
        tipoAcesso: 'link',
        permissao: 'visualizar',
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'ativo',
        configuracoes,
        estatisticas: {
          totalVisualizacoes: 0,
          visualizacoes: []
        }
      };

      // Adicionar data de expiração apenas se fornecida
      if (request.configuracoes?.dataExpiracao) {
        compartilhamento.expiresAt = request.configuracoes.dataExpiracao;
      }

      // Filtrar valores undefined (dupla verificação)
      const dadosLimpos = JSON.parse(JSON.stringify(compartilhamento));

      await db.collection(this.SHARING_COLLECTION).add(dadosLimpos);

      console.log('✅ Link público gerado para lista:', request.listaId);

      return {
        token,
        linkCompartilhamento: `/shared/${token}/list/public`
      };

    } catch (error) {
      console.error('❌ Erro ao gerar link público:', error);
      throw error;
    }
  }

  /**
   * 📨 Criar convite para colaboração
   */
  static async criarConvite(request: {
    listaId: string;
    listaNome: string;
    emailConvidado: string;
    nomeConvidado?: string;
    permissao: 'visualizar' | 'editar' | 'gerenciar';
    mensagem?: string;
  }, dadosConvitador: {
    userId: string;
    nome: string;
    email: string;
  }): Promise<{ conviteId: string; token: string }> {
    try {
      const token = this.gerarToken();
      
      // Data de expiração (7 dias)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      const convite: Omit<ConviteCompartilhamentoLista, 'id'> = {
        listaId: request.listaId,
        listaNome: request.listaNome,
        convidadoPor: dadosConvitador.userId,
        convidadoPorNome: dadosConvitador.nome,
        convidadoPorEmail: dadosConvitador.email,
        emailConvidado: request.emailConvidado,
        nomeConvidado: request.nomeConvidado,
        permissao: request.permissao,
        token,
        expiresAt,
        createdAt: new Date(),
        status: 'pendente',
        mensagem: request.mensagem
      };

      const dadosLimpos = Object.fromEntries(
        Object.entries(convite).filter(([_, value]) => value !== undefined)
      );

      const docRef = await db.collection(this.INVITES_COLLECTION).add(dadosLimpos);

      console.log('✅ Convite criado:', docRef.id);

      // TODO: Implementar envio de email
      await this.enviarEmailConvite(convite, docRef.id);

      return { conviteId: docRef.id, token };

    } catch (error) {
      console.error('❌ Erro ao criar convite:', error);
      throw error;
    }
  }

  /**
   * 📄 Buscar lista compartilhada por token
   */
  static async buscarListaCompartilhada(
    token: string,
    senha?: string
  ): Promise<CompartilhamentoPublicoLista | null> {
    try {
      const snapshot = await db.collection(this.SHARING_COLLECTION)
        .where('token', '==', token)
        .where('status', '==', 'ativo')
        .get();

      if (snapshot.empty) {
        return null;
      }

      const compartilhamento = {
        id: snapshot.docs[0].id,
        ...snapshot.docs[0].data()
      } as CompartilhamentoPublicoLista;

      // Verificar expiração
      if (compartilhamento.expiresAt && new Date() > compartilhamento.expiresAt) {
        await this.marcarComoExpirado(compartilhamento.id);
        return null;
      }

      // Verificar senha se necessário
      if (compartilhamento.configuracoes.senhaProtegido && 
          compartilhamento.configuracoes.senhaProtegido !== senha) {
        throw new Error('Senha incorreta');
      }

      return compartilhamento;

    } catch (error) {
      console.error('❌ Erro ao buscar lista compartilhada:', error);
      throw error;
    }
  }

  /**
   * 📊 Registrar visualização
   */
  static async registrarVisualizacao(
    token: string,
    dadosVisualizacao: {
      ip?: string;
      userAgent?: string;
    }
  ): Promise<void> {
    try {
      const snapshot = await db.collection(this.SHARING_COLLECTION)
        .where('token', '==', token)
        .where('status', '==', 'ativo')
        .get();

      if (!snapshot.empty) {
        const docRef = snapshot.docs[0].ref;
        
        await docRef.update({
          'estatisticas.totalVisualizacoes': (snapshot.docs[0].data().estatisticas?.totalVisualizacoes || 0) + 1,
          'estatisticas.visualizacoes': [
            ...(snapshot.docs[0].data().estatisticas?.visualizacoes || []),
            {
              timestamp: new Date(),
              ip: dadosVisualizacao.ip,
              userAgent: dadosVisualizacao.userAgent
            }
          ].slice(-100) // Manter apenas últimas 100 visualizações
        });
      }

    } catch (error) {
      console.error('❌ Erro ao registrar visualização:', error);
    }
  }

  /**
   * ✅ Aceitar convite
   */
  static async aceitarConvite(
    token: string,
    dadosUsuario: {
      userId: string;
      userName: string;
      userEmail: string;
      avatar?: string;
    }
  ): Promise<string> {
    try {
      // Buscar convite pelo token
      const conviteSnapshot = await db.collection(this.INVITES_COLLECTION)
        .where('token', '==', token)
        .where('status', '==', 'pendente')
        .get();

      if (conviteSnapshot.empty) {
        throw new Error('Convite não encontrado ou já utilizado');
      }

      const conviteDoc = conviteSnapshot.docs[0];
      const convite = { id: conviteDoc.id, ...conviteDoc.data() } as ConviteCompartilhamentoLista;

      // Verificar expiração
      const now = new Date();
      const expiresAt = convite.expiresAt instanceof Date ? 
        convite.expiresAt : 
        new Date(convite.expiresAt);

      if (now > expiresAt) {
        throw new Error('Convite expirado');
      }

      // Criar colaborador
      const colaborador: Omit<ColaboradorLista, 'id'> = {
        listaId: convite.listaId,
        userId: dadosUsuario.userId,
        userEmail: dadosUsuario.userEmail,
        userName: dadosUsuario.userName,
        permissao: convite.permissao,
        convidadoPor: convite.convidadoPor,
        convidadoEm: convite.createdAt instanceof Date ? 
          convite.createdAt : 
          new Date(convite.createdAt),
        aceitouEm: new Date(),
        status: 'ativo',
        avatar: dadosUsuario.avatar
      };

      const dadosLimpos = Object.fromEntries(
        Object.entries(colaborador).filter(([_, value]) => value !== undefined)
      );

      await db.collection(this.COLLABORATORS_COLLECTION).add(dadosLimpos);

      // Marcar convite como aceito
      await db.collection(this.INVITES_COLLECTION)
        .doc(conviteDoc.id)
        .update({ status: 'aceito' });

      console.log('✅ Convite aceito para lista:', convite.listaId);
      return convite.listaId;

    } catch (error) {
      console.error('❌ Erro ao aceitar convite:', error);
      throw error;
    }
  }

  /**
   * 👥 Listar colaboradores de uma lista
   */
  static async listarColaboradores(listaId: string): Promise<ColaboradorLista[]> {
    try {
      const snapshot = await db.collection(this.COLLABORATORS_COLLECTION)
        .where('listaId', '==', listaId)
        .where('status', '==', 'ativo')
        .orderBy('aceitouEm', 'desc')
        .get();

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        convidadoEm: doc.data().convidadoEm instanceof Date ? 
          doc.data().convidadoEm : 
          new Date(doc.data().convidadoEm),
        aceitouEm: doc.data().aceitouEm instanceof Date ? 
          doc.data().aceitouEm : 
          new Date(doc.data().aceitouEm)
      })) as ColaboradorLista[];

    } catch (error) {
      console.error('❌ Erro ao listar colaboradores:', error);
      return [];
    }
  }

  /**
   * 📨 Listar convites pendentes
   */
  static async listarConvitesPendentes(listaId: string): Promise<ConviteCompartilhamentoLista[]> {
    try {
      const snapshot = await db.collection(this.INVITES_COLLECTION)
        .where('listaId', '==', listaId)
        .where('status', '==', 'pendente')
        .orderBy('createdAt', 'desc')
        .get();

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt instanceof Date ? 
          doc.data().createdAt : 
          new Date(doc.data().createdAt),
        expiresAt: doc.data().expiresAt instanceof Date ? 
          doc.data().expiresAt : 
          new Date(doc.data().expiresAt)
      })) as ConviteCompartilhamentoLista[];

    } catch (error) {
      console.error('❌ Erro ao listar convites pendentes:', error);
      return [];
    }
  }

  /**
   * 🔒 Desativar compartilhamento público
   */
  static async desativarCompartilhamento(listaId: string, userId: string): Promise<void> {
    try {
      const snapshot = await db.collection(this.SHARING_COLLECTION)
        .where('listaId', '==', listaId)
        .where('criadoPor', '==', userId)
        .where('status', '==', 'ativo')
        .get();

      if (!snapshot.empty) {
        await snapshot.docs[0].ref.update({
          status: 'inativo',
          updatedAt: new Date()
        });
      }

    } catch (error) {
      console.error('❌ Erro ao desativar compartilhamento:', error);
      throw error;
    }
  }

  /**
   * 🔐 Gerar token único
   */
  private static gerarToken(): string {
    return btoa(Date.now().toString() + Math.random().toString(36))
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 12);
  }

  /**
   * ⏰ Marcar como expirado
   */
  private static async marcarComoExpirado(compartilhamentoId: string): Promise<void> {
    await db.collection(this.SHARING_COLLECTION)
      .doc(compartilhamentoId)
      .update({
        status: 'expirado',
        updatedAt: new Date()
      });
  }

  /**
   * 📧 Enviar email de convite (placeholder)
   */
  private static async enviarEmailConvite(
    convite: Omit<ConviteCompartilhamentoLista, 'id'>,
    conviteId: string
  ): Promise<void> {
    try {
      console.log('📧 Email de convite enviado para:', convite.emailConvidado);
      console.log('🔗 Link do convite:', `/shared/convites/aceitar?token=${convite.token}`);
    } catch (error) {
      console.error('❌ Erro ao enviar email:', error);
    }
  }
} 

