'use client';

import { useState } from 'react';
import { Co<PERSON>, Share2, <PERSON>, Link2, <PERSON>ting<PERSON>, Mail, Eye, Download, Lock, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Lista } from '@/types/list';
import { useToast } from '@/hooks/use-toast';

interface ListShareDialogProps {
  lista: Lista;
  userId: string;
  userName?: string;
  userEmail?: string;
}

/**
 * 🔗 DIALOG DE COMPARTILHAMENTO DE LISTA
 * Permite criar links públicos e enviar convites
 */
export function ListShareDialog({ 
  lista, 
  userId, 
  userName = 'Usuário', 
  userEmail = '<EMAIL>' 
}: ListShareDialogProps) {
  const { toast } = useToast();
  const [aberto, setAberto] = useState(false);
  const [abaAtiva, setAbaAtiva] = useState('link');
  
  // Estados para link público
  const [linkPublico, setLinkPublico] = useState<string | null>(null);
  const [gerandoLink, setGerandoLink] = useState(false);
  const [configuracoes, setConfiguracoes] = useState({
    permitirDownload: true,
    permitirComentarios: false,
    mostrarEstatisticas: false,
    senhaProtegido: '',
    dataExpiracao: ''
  });

  // Estados para convites
  const [emails, setEmails] = useState('');
  const [permissao, setPermissao] = useState<'visualizar' | 'editar' | 'gerenciar'>('visualizar');
  const [mensagem, setMensagem] = useState('');
  const [enviandoConvites, setEnviandoConvites] = useState(false);

  // Gerar link público
  const gerarLinkPublico = async () => {
    try {
      setGerandoLink(true);

      // Filtrar configurações para remover valores undefined
      const configuracoesLimpas = Object.fromEntries(
        Object.entries({
          permitirDownload: configuracoes.permitirDownload,
          permitirComentarios: configuracoes.permitirComentarios,
          mostrarEstatisticas: configuracoes.mostrarEstatisticas,
          ...(configuracoes.senhaProtegido && { senhaProtegido: configuracoes.senhaProtegido }),
          ...(configuracoes.dataExpiracao && { dataExpiracao: new Date(configuracoes.dataExpiracao) })
        }).filter(([_, value]) => value !== undefined)
      );

      const response = await fetch(`/api/lists/${lista.id}/sharing`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tipoCompartilhamento: 'link_publico',
          userId,
          configuracoes: configuracoesLimpas
        })
      });

      const resultado = await response.json();

      if (resultado.success) {
        setLinkPublico(resultado.linkCompleto);
        toast({
          title: '✅ Link gerado com sucesso!',
          description: 'O link público foi criado e está pronto para ser compartilhado.',
        });
      } else {
        throw new Error(resultado.error || 'Erro ao gerar link');
      }

    } catch (error) {
      console.error('❌ Erro ao gerar link:', error);
      toast({
        title: '❌ Erro ao gerar link',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: 'destructive'
      });
    } finally {
      setGerandoLink(false);
    }
  };

  // Copiar link para clipboard
  const copiarLink = async () => {
    if (!linkPublico) return;

    try {
      await navigator.clipboard.writeText(linkPublico);
      toast({
        title: '📋 Link copiado!',
        description: 'O link foi copiado para sua área de transferência.',
      });
    } catch (error) {
      console.error('❌ Erro ao copiar link:', error);
      toast({
        title: '❌ Erro ao copiar',
        description: 'Não foi possível copiar o link.',
        variant: 'destructive'
      });
    }
  };

  // Enviar convites
  const enviarConvites = async () => {
    const emailList = emails.split(',').map(e => e.trim()).filter(e => e);
    if (emailList.length === 0) return;

    try {
      setEnviandoConvites(true);

      const response = await fetch(`/api/lists/${lista.id}/sharing`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tipoCompartilhamento: 'convite',
          userId,
          userNome: userName,
          userEmail,
          emailsUsuarios: emailList,
          permissao,
          mensagem
        })
      });

      const resultado = await response.json();

      if (resultado.success) {
        toast({
          title: '📨 Convites enviados!',
          description: `${emailList.length} convite(s) foram enviados com sucesso.`,
        });
        setEmails('');
        setMensagem('');
      } else {
        throw new Error(resultado.error || 'Erro ao enviar convites');
      }

    } catch (error) {
      console.error('❌ Erro ao enviar convites:', error);
      toast({
        title: '❌ Erro ao enviar convites',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: 'destructive'
      });
    } finally {
      setEnviandoConvites(false);
    }
  };

  return (
    <Dialog open={aberto} onOpenChange={setAberto}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Share2 className="h-4 w-4 mr-2" />
          Compartilhar
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Compartilhar Lista
          </DialogTitle>
          <DialogDescription>
            Compartilhe <strong>{lista.nome}</strong> com outras pessoas ou gere um link público
          </DialogDescription>
        </DialogHeader>

        <Tabs value={abaAtiva} onValueChange={setAbaAtiva}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="link" className="flex items-center gap-2">
              <Link2 className="h-4 w-4" />
              Link Público
            </TabsTrigger>
            <TabsTrigger value="convites" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Convites
            </TabsTrigger>
          </TabsList>

          {/* Aba Link Público */}
          <TabsContent value="link" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Link2 className="h-5 w-5" />
                  Link Público
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Gere um link que permite qualquer pessoa com o link visualizar sua lista
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                {linkPublico ? (
                  <div className="space-y-3">
                    <Label>Link gerado:</Label>
                    <div className="flex gap-2">
                      <Input 
                        value={linkPublico} 
                        readOnly 
                        className="font-mono text-sm"
                      />
                      <Button onClick={copiarLink} size="sm">
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Eye className="h-4 w-4" />
                      <span>Link ativo e pronto para compartilhar</span>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Configurações do Link */}
                    <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                      <h4 className="font-medium flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Configurações do Link
                      </h4>
                      
                      <div className="grid gap-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Permitir download</Label>
                            <p className="text-xs text-muted-foreground">
                              Usuários podem baixar a lista
                            </p>
                          </div>
                          <Switch
                            checked={configuracoes.permitirDownload}
                            onCheckedChange={(checked) => 
                              setConfiguracoes(prev => ({ ...prev, permitirDownload: checked }))
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Permitir comentários</Label>
                            <p className="text-xs text-muted-foreground">
                              Usuários podem deixar comentários
                            </p>
                          </div>
                          <Switch
                            checked={configuracoes.permitirComentarios}
                            onCheckedChange={(checked) => 
                              setConfiguracoes(prev => ({ ...prev, permitirComentarios: checked }))
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Mostrar estatísticas</Label>
                            <p className="text-xs text-muted-foreground">
                              Exibir número de visualizações
                            </p>
                          </div>
                          <Switch
                            checked={configuracoes.mostrarEstatisticas}
                            onCheckedChange={(checked) => 
                              setConfiguracoes(prev => ({ ...prev, mostrarEstatisticas: checked }))
                            }
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Senha de proteção (opcional)</Label>
                          <Input
                            type="password"
                            placeholder="Deixe vazio para link público"
                            value={configuracoes.senhaProtegido}
                            onChange={(e) => 
                              setConfiguracoes(prev => ({ ...prev, senhaProtegido: e.target.value }))
                            }
                          />
                          <p className="text-xs text-muted-foreground">
                            Se definida, usuários precisarão da senha para acessar
                          </p>
                        </div>

                        <div className="space-y-2">
                          <Label>Data de expiração (opcional)</Label>
                          <Input
                            type="datetime-local"
                            value={configuracoes.dataExpiracao}
                            onChange={(e) => 
                              setConfiguracoes(prev => ({ ...prev, dataExpiracao: e.target.value }))
                            }
                          />
                          <p className="text-xs text-muted-foreground">
                            Deixe vazio para link permanente
                          </p>
                        </div>
                      </div>
                    </div>

                    <Button 
                      onClick={gerarLinkPublico} 
                      className="w-full"
                      disabled={gerandoLink}
                    >
                      {gerandoLink ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Gerando Link...
                        </>
                      ) : (
                        <>
                          <Link2 className="h-4 w-4 mr-2" />
                          Gerar Link Público
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba Convites */}
          <TabsContent value="convites" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Enviar Convites
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Convide pessoas específicas para colaborar na sua lista
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>E-mails dos convidados</Label>
                  <Textarea
                    placeholder="Digite os e-mails separados por vírgula"
                    value={emails}
                    onChange={(e) => setEmails(e.target.value)}
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    Exemplo: <EMAIL>, <EMAIL>
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Nível de permissão</Label>
                  <div className="flex gap-2">
                    <Button
                      variant={permissao === 'visualizar' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPermissao('visualizar')}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Visualizar
                    </Button>
                    <Button
                      variant={permissao === 'editar' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPermissao('editar')}
                    >
                      Editar
                    </Button>
                    <Button
                      variant={permissao === 'gerenciar' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setPermissao('gerenciar')}
                    >
                      Gerenciar
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Mensagem personalizada (opcional)</Label>
                  <Textarea
                    placeholder="Adicione uma mensagem para os convidados"
                    value={mensagem}
                    onChange={(e) => setMensagem(e.target.value)}
                    rows={2}
                  />
                </div>

                <Button 
                  onClick={enviarConvites} 
                  className="w-full"
                  disabled={enviandoConvites || !emails.trim()}
                >
                  {enviandoConvites ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Enviando Convites...
                    </>
                  ) : (
                    <>
                      <Mail className="h-4 w-4 mr-2" />
                      Enviar Convites
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Informações da Lista */}
        <Card className="mt-6">
          <CardContent className="pt-6">
            <div className="flex items-start gap-4">
              <div className="flex-1">
                <h4 className="font-medium">{lista.nome}</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  {lista.descricao || 'Sem descrição'}
                </p>
                <div className="flex gap-2">
                  <Badge variant="secondary" className="text-xs">
                    {lista.tipoLista}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {lista.tamanho} itens
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
} 

