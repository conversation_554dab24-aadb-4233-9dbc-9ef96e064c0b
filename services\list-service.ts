import { 
  Lista, 
  ItemLista, 
  CriarListaData, 
  AtualizarListaData, 
  AdicionarItemListaData,
  FiltrosLista,
  ResultadoBuscaListas,
  CompartilharListaData,
  OperacaoLoteListaData,
  ResultadoOperacaoLote
} from '@/types/list';
import { IsolationUtils, IsolationError, ISOLATION_ERRORS } from '@/lib/utils/isolation';
import { db, FieldValue } from '@/lib/firebase-admin';

/**
 * 📝 SERVICE PARA OPERAÇÕES COM LISTAS
 * Implementa todas as operações CRUD e funcionalidades avançadas
 */
export class ListService {
  private static COLLECTION_NAME = 'lists';
  private static LIST_ITEMS_COLLECTION = 'list_items';

  /**
   * Buscar dados das marcas pelos IDs
   */
  private static async buscarMarcasIds(marcasIds: string[], userId: string): Promise<Array<{id: string; name: string; logo?: string}>> {
    if (!marcasIds || marcasIds.length === 0) {
      console.log('[LIST_SERVICE_FETCH_BRANDS] Nenhum ID de marca fornecido');
      return [];
    }

    console.log('[LIST_SERVICE_FETCH_BRANDS] Buscando marcas:', { marcasIds, userId });

    try {
      // Buscar marcas em lotes de 10 (limite do Firestore para arrays)
      const marcasData: Array<{id: string; name: string; logo?: string}> = [];
      const batches: string[][] = [];
      for (let i = 0; i < marcasIds.length; i += 10) {
        batches.push(marcasIds.slice(i, i + 10));
      }

      console.log('[LIST_SERVICE_FETCH_BRANDS] Processando em batches:', batches.length);

      for (const batch of batches) {
        console.log('[LIST_SERVICE_FETCH_BRANDS] Processando batch:', batch);
        
        const marcasSnapshot = await db.collection('brands')
          .where('userId', '==', userId)
          .where('__name__', 'in', batch)
          .get();

        console.log('[LIST_SERVICE_FETCH_BRANDS] Snapshot resultado:', { 
          batchSize: batch.length, 
          foundDocs: marcasSnapshot.size 
        });

        marcasSnapshot.docs.forEach(doc => {
          const marcaData = doc.data();
          console.log('[LIST_SERVICE_FETCH_BRANDS] Marca encontrada:', { 
            id: doc.id, 
            name: marcaData.name, 
            logo: marcaData.logo 
          });
          
          marcasData.push({
            id: doc.id,
            name: marcaData.name,
            logo: marcaData.logo
          });
        });
      }

      console.log('[LIST_SERVICE_FETCH_BRANDS] Resultado final:', { 
        inputIds: marcasIds.length, 
        foundBrands: marcasData.length,
        brandData: marcasData
      });

      return marcasData;
    } catch (error) {
      console.error('[LIST_SERVICE_FETCH_BRANDS] Erro ao buscar marcas:', { marcasIds, userId, error });
      return [];
    }
  }

  /**
   * Popular marcas associadas nas listas
   */
  private static async popularMarcasAssociadas(listas: Lista[], userId: string): Promise<Lista[]> {
    // Coletar todos os IDs de marcas únicos
    const todasMarcasIds = new Set<string>();
    listas.forEach(lista => {
      if (lista.marcasAssociadas) {
        lista.marcasAssociadas.forEach(marca => {
          if (typeof marca === 'string') {
            todasMarcasIds.add(marca);
          } else if (marca.id) {
            todasMarcasIds.add(marca.id);
          }
        });
      }
    });

    if (todasMarcasIds.size === 0) {
      return listas.map(lista => ({
        ...lista,
        marcasAssociadas: lista.marcasAssociadas || []
      }));
    }

    // Buscar dados das marcas
    const marcasData = await this.buscarMarcasIds(Array.from(todasMarcasIds), userId);
    const marcasMap = new Map(marcasData.map(marca => [marca.id, marca]));

    // Popular os dados nas listas
    return listas.map(lista => ({
      ...lista,
      marcasAssociadas: (lista.marcasAssociadas || []).map(marca => {
        if (typeof marca === 'string') {
          return marcasMap.get(marca) || { id: marca, name: 'Marca não encontrada' };
        }
        return marca;
      }).filter(marca => marca.name !== 'Marca não encontrada')
    }));
  }

  /**
   * Criar nova lista
   */
  static async criarLista(data: CriarListaData, userId: string, userDisplayName: string): Promise<Lista> {
    try {
      console.log('[LIST_SERVICE_CREATE] Iniciando criação de lista:', { 
        nome: data.nome,
        marcasAssociadas: data.marcasAssociadas,
        userId 
      });

      // Buscar dados das marcas se fornecidas
      let marcasAssociadas: Array<{id: string; name: string; logo?: string}> = [];
      if (data.marcasAssociadas && data.marcasAssociadas.length > 0) {
        console.log('[LIST_SERVICE_CREATE] Buscando dados das marcas:', data.marcasAssociadas);
        marcasAssociadas = await this.buscarMarcasIds(data.marcasAssociadas, userId);
        console.log('[LIST_SERVICE_CREATE] Marcas encontradas:', marcasAssociadas);
      } else {
        console.log('[LIST_SERVICE_CREATE] Nenhuma marca associada fornecida');
      }

      // Preparar dados com isolamento
      const dadosCompletos = IsolationUtils.prepareCreateData(
        {
          ...data,
          criadoPor: userId,
          criadoPorNome: userDisplayName,
          tamanho: 0,
          tags: data.tags || [],
          marcasAssociadas: marcasAssociadas,
          isPublica: data.isPublica || false,
          compartilhadaCom: [],
          permissoes: {
            visualizar: [],
            editar: [],
            gerenciar: []
          },
          criterios: data.criterios || [],
          configuracaoAtualizacao: {
            frequencia: 'manual' as const,
            ultimaExecucao: null,
            proximaExecucao: null,
            ativa: false
          },
          estatisticas: {
            totalVisualizacoes: 0,
            ultimaVisualizacao: null,
            totalCompartilhamentos: 0,
            totalExportacoes: 0
          },
          status: 'ativa' as const,
          configuracaoExportacao: {
            formatosPadrao: ['csv', 'xlsx'],
            incluirMetadados: true,
            incluirEstatisticas: false
          }
        },
        userId,
        userId
      );

      console.log('[LIST_SERVICE_CREATE] Dados completos preparados:', {
        nome: dadosCompletos.nome,
        marcasAssociadas: dadosCompletos.marcasAssociadas,
        totalCampos: Object.keys(dadosCompletos).length
      });

      const docRef = await db.collection(this.COLLECTION_NAME).add(dadosCompletos);
      
      console.log('[LIST_SERVICE_CREATE] Lista criada no Firestore:', docRef.id);

      // Log da criação
      IsolationUtils.logIsolationEvent(
        'create',
        'lists',
        docRef.id,
        userId,
        { 
          nome: data.nome,
          tipoLista: data.tipoLista,
          tipoObjeto: data.tipoObjeto,
          marcasAssociadas: marcasAssociadas.length
        }
      );

      const resultado = {
        id: docRef.id,
        ...dadosCompletos
      } as Lista;

      console.log('[LIST_SERVICE_CREATE] Lista criada com sucesso:', {
        id: resultado.id,
        nome: resultado.nome,
        marcasAssociadas: resultado.marcasAssociadas?.length || 0
      });

      return resultado;

    } catch (error: any) {
      console.error('[LIST_SERVICE_CREATE] Erro ao criar lista:', { data, userId, error });
      throw new Error(`Erro ao criar lista: ${error.message}`);
    }
  }

  /**
   * Buscar lista por ID
   */
  static async buscarListaPorId(listaId: string, userId: string): Promise<Lista | null> {
    try {
      const docRef = db.collection(this.COLLECTION_NAME).doc(listaId);
      const docSnap = await docRef.get();

      if (!docSnap.exists) {
        return null;
      }

      const lista = { id: docSnap.id, ...docSnap.data() } as Lista;

      // Verificar permissões de acesso
      if (!this.podeAcessarLista(lista, userId)) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para acessar esta lista',
          userId,
          listaId
        );
      }

      // Popular marcas associadas
      const listasComMarcas = await this.popularMarcasAssociadas([lista], userId);

      // Incrementar visualizações se não for o proprietário
      if (lista.criadoPor !== userId) {
        await this.incrementarVisualizacoes(listaId);
      }

      return listasComMarcas[0];

    } catch (error: any) {
      if (error instanceof IsolationError) {
        throw error;
      }
      console.error('[LIST_SERVICE_GET_BY_ID]', { listaId, userId, error });
      throw new Error(`Erro ao buscar lista: ${error.message}`);
    }
  }

  /**
   * Buscar listas do usuário com filtros
   */
  static async buscarListasUsuario(
    userId: string, 
    filtros: FiltrosLista = {}
  ): Promise<ResultadoBuscaListas> {
    try {
      console.log('[LIST_SERVICE_SEARCH] Iniciando busca:', { userId, filtros });
      
      // Query mais simples para evitar problemas com índices
      let query = db.collection(this.COLLECTION_NAME)
        .where('criadoPor', '==', userId);

      console.log('[LIST_SERVICE_SEARCH] Query configurada para userId:', userId);

      // Aplicar filtros
      if (filtros.tipoLista) {
        query = query.where('tipoLista', '==', filtros.tipoLista);
      }
      if (filtros.tipoObjeto) {
        query = query.where('tipoObjeto', '==', filtros.tipoObjeto);
      }
      if (filtros.status) {
        query = query.where('status', '==', filtros.status);
      }

      const snapshot = await query.get();
      console.log('[LIST_SERVICE_SEARCH] Snapshot obtido:', snapshot.size, 'documentos');
      
      let listas = snapshot.docs.map(doc => ({ 
        id: doc.id, 
        ...doc.data() 
      })) as Lista[];

      console.log('[LIST_SERVICE_SEARCH] Listas após mapeamento:', listas.length);

      // Popular marcas associadas
      listas = await this.popularMarcasAssociadas(listas, userId);

      // Filtros adicionais em memória
      if (filtros.busca) {
        const busca = filtros.busca.toLowerCase();
        listas = listas.filter(lista => 
          lista.nome.toLowerCase().includes(busca) ||
          (lista.descricao && lista.descricao.toLowerCase().includes(busca)) ||
          lista.tags.some(tag => tag.toLowerCase().includes(busca))
        );
      }

      if (filtros.tags && filtros.tags.length > 0) {
        listas = listas.filter(lista =>
          filtros.tags!.some(tag => lista.tags.includes(tag))
        );
      }

      const resultado = {
        listas,
        total: listas.length,
        pagina: 1,
        porPagina: listas.length || 20,
        totalPaginas: 1
      };

      console.log('[LIST_SERVICE_SEARCH] Resultado final:', resultado);
      return resultado;

    } catch (error: any) {
      console.error('[LIST_SERVICE_GET_USER_LISTS]', { userId, filtros, error });
      throw new Error(`Erro ao buscar listas do usuário: ${error.message}`);
    }
  }

  /**
   * Atualizar lista
   */
  static async atualizarLista(
    listaId: string, 
    data: AtualizarListaData, 
    userId: string
  ): Promise<Lista> {
    try {
      // Verificar ownership
      const lista = await this.buscarListaPorId(listaId, userId);
      if (!lista) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Lista não encontrada',
          userId,
          listaId
        );
      }

      if (!this.podeEditarLista(lista, userId)) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para editar esta lista',
          userId,
          listaId
        );
      }

      // Processar marcas associadas se fornecidas
      let dadosCompletos = { ...data };
      if (data.marcasAssociadas !== undefined) {
        const marcasData = await this.buscarMarcasIds(data.marcasAssociadas, userId);
        dadosCompletos.marcasAssociadas = marcasData as any;
      }

      // Preparar dados de atualização
      const dadosAtualizacao = IsolationUtils.prepareUpdateData(dadosCompletos, userId);

      const docRef = db.collection(this.COLLECTION_NAME).doc(listaId);
      await docRef.update(dadosAtualizacao as any);

      // Log da atualização
      IsolationUtils.logIsolationEvent(
        'update',
        'lists',
        listaId,
        userId,
        { 
          camposAlterados: Object.keys(data),
          marcasAssociadas: (dadosCompletos.marcasAssociadas as any)?.length || 0
        }
      );

      // Retornar lista atualizada com marcas populadas
      const listaAtualizada = { ...lista, ...dadosAtualizacao } as Lista;
      const listasComMarcas = await this.popularMarcasAssociadas([listaAtualizada], userId);
      return listasComMarcas[0];

    } catch (error: any) {
      if (error instanceof IsolationError) {
        throw error;
      }
      console.error('[LIST_SERVICE_UPDATE]', { listaId, data, userId, error });
      throw new Error(`Erro ao atualizar lista: ${error.message}`);
    }
  }

  /**
   * Deletar lista permanentemente
   */
  static async deletarLista(listaId: string, userId: string): Promise<void> {
    try {
      // Verificar ownership
      const lista = await this.buscarListaPorId(listaId, userId);
      if (!lista) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Lista não encontrada',
          userId,
          listaId
        );
      }

      if (!this.podeGerenciarLista(lista, userId)) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para deletar esta lista',
          userId,
          listaId
        );
      }

      const batch = db.batch();

      // Deletar permanentemente a lista
      const listaRef = db.collection(this.COLLECTION_NAME).doc(listaId);
      batch.delete(listaRef);

      // Deletar permanentemente todos os itens da lista
      const itensQuery = db.collection(this.LIST_ITEMS_COLLECTION)
        .where('listaId', '==', listaId);
      
      const itensSnapshot = await itensQuery.get();
      
      itensSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();

      // Log da deleção permanente
      IsolationUtils.logIsolationEvent(
        'delete',
        'lists',
        listaId,
        userId,
        { 
          nome: lista.nome,
          totalItens: lista.tamanho,
          totalItensRemovidos: itensSnapshot.size,
          tipoDeletacao: 'permanente'
        }
      );

    } catch (error: any) {
      if (error instanceof IsolationError) {
        throw error;
      }
      console.error('[LIST_SERVICE_DELETE_PERMANENT]', { listaId, userId, error });
      throw new Error(`Erro ao deletar lista permanentemente: ${error.message}`);
    }
  }

  /**
   * Adicionar item à lista
   */
  static async adicionarItemLista(data: AdicionarItemListaData, userId: string): Promise<ItemLista> {
    try {
      // Verificar se a lista existe e se o usuário pode editá-la
      const lista = await this.buscarListaPorId(data.listaId, userId);
      if (!lista) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Lista não encontrada',
          userId,
          data.listaId
        );
      }

      if (!this.podeEditarLista(lista, userId)) {
        throw new IsolationError(
          ISOLATION_ERRORS.OWNERSHIP_DENIED,
          'Usuário não tem permissão para adicionar itens nesta lista',
          userId,
          data.listaId
        );
      }

      // Verificar se o item já está na lista
      const itemExistente = await this.verificarItemNaLista(data.listaId, data.itemId);
      if (itemExistente) {
        throw new Error('Item já está presente na lista');
      }

      // Criar itemData básico apenas com referência
      const itemData = {
        itemId: data.itemId,
        tipoItem: data.tipoItem
      };

      // Calcular próxima posição
      const proximaPosicao = await this.calcularProximaPosicao(data.listaId);

      const dadosItem = IsolationUtils.prepareCreateData(
        {
          listaId: data.listaId,
          itemId: data.itemId,
          tipoItem: data.tipoItem,
          dataAdicao: new Date(),
          adicionadoPor: userId,
          posicao: proximaPosicao,
          itemData,
          status: 'ativo' as const,
          notas: data.notas || '',
          tagsPersonalizadas: data.tagsPersonalizadas || []
        },
        userId,
        userId
      );

      // Filtrar valores undefined para Firestore
      const dadosLimpos = this.filterUndefinedValues(dadosItem);

      console.log('[LIST_SERVICE_ADD_ITEM] Salvando item no Firestore:', {
        collection: this.LIST_ITEMS_COLLECTION,
        dadosLimpos: {
          listaId: dadosLimpos.listaId,
          itemId: dadosLimpos.itemId,
          tipoItem: dadosLimpos.tipoItem,
          status: dadosLimpos.status,
          posicao: dadosLimpos.posicao,
          adicionadoPor: dadosLimpos.adicionadoPor
        }
      });

      const docRef = await db.collection(this.LIST_ITEMS_COLLECTION).add(dadosLimpos);

      console.log('[LIST_SERVICE_ADD_ITEM] Item salvo com sucesso:', {
        docId: docRef.id,
        collection: this.LIST_ITEMS_COLLECTION
      });

      // Incrementar tamanho da lista
      await this.incrementarTamanhoLista(data.listaId);

      // Log da adição
      IsolationUtils.logIsolationEvent(
        'create',
        'list_items',
        docRef.id,
        userId,
        { 
          listaId: data.listaId,
          itemId: data.itemId,
          tipoItem: data.tipoItem
        }
      );

      return {
        id: docRef.id,
        ...dadosItem
      } as unknown as ItemLista;

    } catch (error: any) {
      console.error('[LIST_SERVICE_ADD_ITEM]', { data, userId, error });
      throw new Error(`Erro ao adicionar item à lista: ${error.message}`);
    }
  }

  /**
   * Buscar itens de uma lista
   */
  static async buscarItensLista(
    listaId: string, 
    userId: string,
    limite: number = 50,
    offset: number = 0
  ): Promise<{ itens: ItemLista[]; total: number }> {
    try {
      // Verificar acesso à lista
      const lista = await this.buscarListaPorId(listaId, userId);
      if (!lista) {
        throw new IsolationError(
          ISOLATION_ERRORS.DOCUMENT_NOT_FOUND,
          'Lista não encontrada',
          userId,
          listaId
        );
      }

      console.log('[LIST_SERVICE_GET_ITEMS] Executando query:', {
        collection: this.LIST_ITEMS_COLLECTION,
        listaId,
        status: 'ativo',
        limite: limite + offset
      });

      const query = db.collection(this.LIST_ITEMS_COLLECTION)
        .where('listaId', '==', listaId)
        .where('status', '==', 'ativo')
        .orderBy('posicao', 'asc')
        .limit(limite + offset);

      const snapshot = await query.get();

      console.log('[LIST_SERVICE_GET_ITEMS] Resultado da query:', {
        empty: snapshot.empty,
        size: snapshot.size,
        docs: snapshot.docs.length
      });

      const todosItens = snapshot.docs.map(doc => {
        const data = doc.data();
        console.log('[LIST_SERVICE_GET_ITEMS] Item encontrado:', {
          id: doc.id,
          listaId: data.listaId,
          itemId: data.itemId,
          status: data.status,
          posicao: data.posicao
        });
        return {
          id: doc.id,
          ...data
        };
      }) as ItemLista[];

      // Aplicar offset manualmente (Firestore não tem offset nativo)
      const itens = todosItens.slice(offset, offset + limite);

      console.log('[LIST_SERVICE_GET_ITEMS] Resultado final:', {
        todosItensCount: todosItens.length,
        itensCount: itens.length,
        offset,
        limite,
        listaTamanho: lista.tamanho
      });

      return {
        itens,
        total: lista.tamanho
      };

    } catch (error: any) {
      if (error instanceof IsolationError) {
        throw error;
      }
      console.error('[LIST_SERVICE_GET_ITEMS]', { listaId, userId, error });
      throw new Error(`Erro ao buscar itens da lista: ${error.message}`);
    }
  }

  /**
   * Operações em lote
   */
  static async operacaoLote(
    data: OperacaoLoteListaData, 
    userId: string
  ): Promise<ResultadoOperacaoLote> {
    try {
      const resultado: ResultadoOperacaoLote = {
        sucesso: [],
        falhas: []
      };

      for (const listaId of data.listaIds) {
        try {
          switch (data.operacao) {
            case 'arquivar':
              // Atualizar diretamente o documento para incluir status
              const docRef = db.collection(this.COLLECTION_NAME).doc(listaId);
              await docRef.update({ 
                status: 'arquivada',
                updatedAt: new Date(),
                updatedBy: userId
              });
              resultado.sucesso.push(listaId);
              break;
            case 'deletar':
              await this.deletarLista(listaId, userId);
              resultado.sucesso.push(listaId);
              break;
            default:
              resultado.falhas.push({ id: listaId, erro: 'Operação não suportada' });
          }
        } catch (error: any) {
          resultado.falhas.push({ 
            id: listaId, 
            erro: error.message 
          });
        }
      }

      return resultado;

    } catch (error: any) {
      console.error('[LIST_SERVICE_BATCH]', { data, userId, error });
      throw new Error(`Erro na operação em lote: ${error.message}`);
    }
  }

  // Métodos privados auxiliares
  private static podeAcessarLista(lista: Lista, userId: string): boolean {
    return lista.criadoPor === userId ||
           lista.isPublica ||
           lista.permissoes.visualizar.includes(userId) ||
           lista.permissoes.editar.includes(userId) ||
           lista.permissoes.gerenciar.includes(userId);
  }

  private static podeEditarLista(lista: Lista, userId: string): boolean {
    return lista.criadoPor === userId ||
           lista.permissoes.editar.includes(userId) ||
           lista.permissoes.gerenciar.includes(userId);
  }

  private static podeGerenciarLista(lista: Lista, userId: string): boolean {
    return lista.criadoPor === userId ||
           lista.permissoes.gerenciar.includes(userId);
  }

  private static async verificarItemNaLista(listaId: string, itemId: string): Promise<boolean> {
    try {
      const query = db.collection(this.LIST_ITEMS_COLLECTION)
        .where('listaId', '==', listaId)
        .where('itemId', '==', itemId)
        .where('status', '==', 'ativo')
        .limit(1);

      const snapshot = await query.get();
      return !snapshot.empty;
    } catch (error: any) {
      console.error('[LIST_SERVICE_CHECK_ITEM]', { listaId, itemId, error });
      return false;
    }
  }

  private static async calcularProximaPosicao(listaId: string): Promise<number> {
    try {
      const query = db.collection(this.LIST_ITEMS_COLLECTION)
        .where('listaId', '==', listaId)
        .where('status', '==', 'ativo')
        .orderBy('posicao', 'desc')
        .limit(1);

      const snapshot = await query.get();
      
      if (snapshot.empty) {
        return 1;
      }

      const ultimoItem = snapshot.docs[0].data() as ItemLista;
      return (ultimoItem.posicao || 0) + 1;

    } catch (error: any) {
      console.error('[LIST_SERVICE_CALC_POSITION]', { listaId, error });
      return 1; // Fallback para primeira posição
    }
  }

  private static async incrementarTamanhoLista(listaId: string): Promise<void> {
    try {
      const docRef = db.collection(this.COLLECTION_NAME).doc(listaId);
      await docRef.update({
                 tamanho: FieldValue.increment(1),
        ultimaAtualizacao: new Date()
      });
    } catch (error: any) {
      console.error('[LIST_SERVICE_INCREMENT_SIZE]', { listaId, error });
      // Não lançar erro para não quebrar o fluxo principal
    }
  }

  private static async incrementarVisualizacoes(listaId: string): Promise<void> {
    try {
      const docRef = db.collection(this.COLLECTION_NAME).doc(listaId);
      await docRef.update({
                 'estatisticas.totalVisualizacoes': FieldValue.increment(1),
        'estatisticas.ultimaVisualizacao': new Date()
      });
    } catch (error: any) {
      console.error('[LIST_SERVICE_INCREMENT_VIEWS]', { listaId, error });
      // Não lançar erro para não quebrar o fluxo principal
    }
  }

  private static filterUndefinedValues(obj: any): any {
    if (obj === null || obj === undefined) {
      return null;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.filterUndefinedValues(item));
    }
    
    if (typeof obj === 'object') {
      const filtered: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (value !== undefined) {
          filtered[key] = this.filterUndefinedValues(value);
        }
      }
      return filtered;
    }
    
    return obj;
  }
} 

