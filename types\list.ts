import { BaseDocument } from './base';

/**
 * 📝 TIPOS PARA SISTEMA DE LISTAS
 * Baseado na estrutura criada no Firestore
 */

export type TipoLista = 'estática' | 'dinâmica';
export type TipoObjeto = 'influenciadores' | 'marcas' | 'campanhas' | 'conteúdo';
export type StatusLista = 'ativa' | 'arquivada' | 'deletada';
export type FrequenciaAtualizacao = 'manual' | 'diaria' | 'semanal' | 'mensal';

export interface PermissoesLista {
  visualizar: string[]; // userIds que podem visualizar
  editar: string[];     // userIds que podem editar
  gerenciar: string[];  // userIds que podem gerenciar compartilhamento
}

export interface CriterioLista {
  campo: string;
  operador: string;
  valor: string;
}

export interface ConfiguracaoAtualizacao {
  frequencia: FrequenciaAtualizacao;
  ultimaExecucao: Date | null;
  proximaExecucao: Date | null;
  ativa: boolean;
}

export interface EstatisticasLista {
  totalVisualizacoes: number;
  ultimaVisualizacao: Date | null;
  totalCompartilhamentos: number;
  totalExportacoes: number;
}

export interface ConfiguracaoExportacao {
  formatosPadrao: string[];
  incluirMetadados: boolean;
  incluirEstatisticas: boolean;
}

export interface Lista extends BaseDocument {
  // Dados básicos
  nome: string;
  descricao: string;
  tipoLista: TipoLista;
  tipoObjeto: TipoObjeto;
  
  // Propriedades do criador
  criadoPor: string;
  criadoPorNome: string;
  
  // Metadados
  tamanho: number;
  tags: string[];
  
  // Marcas associadas
  marcasAssociadas: Array<{
    id: string;
    name: string;
    logo?: string;
  }>;
  
  // Privacidade e compartilhamento
  isPublica: boolean;
  compartilhadaCom: string[];
  permissoes: PermissoesLista;
  
  // Critérios para listas dinâmicas
  criterios: CriterioLista[];
  
  // Configurações de atualização
  configuracaoAtualizacao: ConfiguracaoAtualizacao;
  
  // Estatísticas
  estatisticas: EstatisticasLista;
  
  // Status
  status: StatusLista;
  
  // Configurações de exportação
  configuracaoExportacao: ConfiguracaoExportacao;
}

export interface ItemLista extends BaseDocument {
  listaId: string;
  itemId: string;        // ID do item original (influencer, marca, etc.)
  tipoItem: string;      // Tipo do item para facilitar queries
  
  // Dados temporais
  dataAdicao: Date;
  adicionadoPor: string; // userId de quem adicionou
  
  // Posição para ordenação manual
  posicao: number;
  
  // Snapshot dos dados do item (para performance)
  itemData: {
    nome: string;
    foto?: string;
    [key: string]: any;
  };
  
  // Status do item na lista
  status: 'ativo' | 'removido';
  
  // Notas específicas para este item nesta lista
  notas: string;
  
  // Tags específicas para este item nesta lista
  tagsPersonalizadas: string[];
}

// Tipos para operações de criação
export interface CriarListaData {
  nome: string;
  descricao?: string;
  tipoLista: TipoLista;
  tipoObjeto: TipoObjeto;
  tags?: string[];
  isPublica?: boolean;
  criterios?: CriterioLista[];
  marcasAssociadas?: string[]; // IDs das marcas selecionadas
  autoImportBrands?: string[]; // IDs das marcas para auto-importação de influenciadores
}

export interface AdicionarItemListaData {
  listaId: string;
  itemId: string;
  tipoItem: string;
  notas?: string;
  tagsPersonalizadas?: string[];
}

export interface AtualizarListaData {
  nome?: string;
  descricao?: string;
  tags?: string[];
  isPublica?: boolean;
  criterios?: CriterioLista[];
  configuracaoAtualizacao?: Partial<ConfiguracaoAtualizacao>;
  marcasAssociadas?: string[]; // IDs das marcas selecionadas
}

// Tipos para filtros e queries
export interface FiltrosLista {
  tipoLista?: TipoLista;
  tipoObjeto?: TipoObjeto;
  status?: StatusLista;
  tags?: string[];
  isPublica?: boolean;
  busca?: string;
}

export interface ResultadoBuscaListas {
  listas: Lista[];
  total: number;
  pagina: number;
  porPagina: number;
  totalPaginas: number;
}

// Tipos para compartilhamento
export interface CompartilharListaData {
  emailsUsuarios: string[];
  permissao: 'visualizar' | 'editar' | 'gerenciar';
  mensagem?: string;
}

// Tipos para compartilhamento público
export interface CompartilhamentoPublicoLista {
  id: string;
  listaId: string;
  listaNome: string;
  criadoPor: string;
  criadoPorNome: string;
  token: string; // Token único para acesso público
  tipoAcesso: 'publico' | 'link'; // publico = qualquer um pode ver, link = só quem tem o link
  permissao: 'visualizar' | 'comentar';
  expiresAt?: Date; // Data de expiração (opcional)
  createdAt: Date;
  updatedAt: Date;
  status: 'ativo' | 'inativo' | 'expirado';
  configuracoes: {
    permitirDownload: boolean;
    permitirComentarios: boolean;
    mostrarEstatisticas: boolean;
    senhaProtegido?: string;
  };
  estatisticas: {
    totalVisualizacoes: number;
    visualizacoes: Array<{
      timestamp: Date;
      ip?: string;
      userAgent?: string;
    }>;
  };
}

export interface ConviteCompartilhamentoLista {
  id: string;
  listaId: string;
  listaNome: string;
  convidadoPor: string;
  convidadoPorNome: string;
  convidadoPorEmail: string;
  emailConvidado: string;
  nomeConvidado?: string;
  permissao: 'visualizar' | 'editar' | 'gerenciar';
  token: string;
  expiresAt: Date;
  createdAt: Date;
  status: 'pendente' | 'aceito' | 'recusado' | 'expirado';
  mensagem?: string;
}

export interface ColaboradorLista {
  id: string;
  listaId: string;
  userId: string;
  userEmail: string;
  userName: string;
  permissao: 'visualizar' | 'editar' | 'gerenciar';
  convidadoPor: string;
  convidadoEm: Date;
  aceitouEm: Date;
  status: 'ativo' | 'inativo';
  avatar?: string;
}

export interface SolicitacaoCompartilhamentoLista {
  listaId: string;
  emailsUsuarios?: string[];
  permissao: 'visualizar' | 'editar' | 'gerenciar';
  mensagem?: string;
  // Para compartilhamento público
  tipoCompartilhamento: 'convite' | 'link_publico';
  configuracoes?: {
    permitirDownload?: boolean;
    permitirComentarios?: boolean;
    mostrarEstatisticas?: boolean;
    senhaProtegido?: string;
    dataExpiracao?: Date;
  };
}

export interface PermissaoUsuarioLista {
  userId: string;
  userEmail: string;
  userName: string;
  permissao: 'visualizar' | 'editar' | 'gerenciar';
  dataCompartilhamento: Date;
  compartilhadoPor: string;
}

// Tipos para operações em lote
export interface OperacaoLoteListaData {
  listaIds: string[];
  operacao: 'arquivar' | 'deletar' | 'compartilhar' | 'mover';
  parametros?: Record<string, any>;
}

export interface ResultadoOperacaoLote {
  sucesso: string[];
  falhas: Array<{
    id: string;
    erro: string;
  }>;
}

// Tipos para exportação
export interface ConfiguracaoExportacaoLista {
  formato: 'csv' | 'xlsx' | 'json';
  incluirMetadados: boolean;
  incluirEstatisticas: boolean;
  camposPersonalizados?: string[];
}

export interface DadosExportacaoLista {
  lista: Lista;
  itens: ItemLista[];
  metadados?: Record<string, any>;
  estatisticas?: Record<string, any>;
}

// Tipos para critérios de listas dinâmicas
export interface CamposCriterio {
  influenciadores: Array<{
    valor: string;
    label: string;
    tipo: 'number' | 'string' | 'select' | 'range';
    opcoes?: string[];
  }>;
  marcas: Array<{
    valor: string;
    label: string;
    tipo: 'number' | 'string' | 'select' | 'range';
    opcoes?: string[];
  }>;
  campanhas: Array<{
    valor: string;
    label: string;
    tipo: 'number' | 'string' | 'select' | 'range';
    opcoes?: string[];
  }>;
  conteúdo: Array<{
    valor: string;
    label: string;
    tipo: 'number' | 'string' | 'select' | 'range';
    opcoes?: string[];
  }>;
}

export interface OperadoresCriterio {
  number: Array<{ valor: string; label: string }>;
  string: Array<{ valor: string; label: string }>;
  select: Array<{ valor: string; label: string }>;
  range: Array<{ valor: string; label: string }>;
}

// Tipos para validação
export interface ValidacaoLista {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

export interface ValidacaoItemLista {
  isValid: boolean;
  itemExiste: boolean;
  jaAdicionado: boolean;
  pertenceAoUsuario: boolean;
  errors: string[];
} 

