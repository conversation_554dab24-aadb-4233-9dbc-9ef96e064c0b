'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { ColumnDef } from "@tanstack/react-table";
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useDataTable } from '@/hooks/use-data-table';
import { createInfluencerColumns, Influenciador as InfluencerType } from '@/lib/table-columns/influencers';
import { DataTable } from '@/components/ui/data-table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  ArrowLeft, 
  Search, 
  Eye, 
  Copy,
  Shield,
  List,
  Users,
  Tag,
  Calendar,
  ImageIcon,
  Download,
  Share2,
  Lock,
  Unlock
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Loader } from '@/components/ui/loader';

// Tipos da lista
interface Lista {
  id: string;
  nome: string;
  tipoLista: 'estática' | 'dinâmica';
  tipoObjeto: 'influenciadores' | 'marcas' | 'campanhas' | 'conteúdo';
  tamanho: number;
  criadoPor: string;
  criadoPorNome: string;
  createdAt: string;
  updatedAt: string;
  descricao?: string;
  tags?: string[];
  criterios?: {
    campo: string;
    operador: string;
    valor: string;
  }[];
}

// Tipos específicos para cada objeto
interface Influenciador {
  id: string;
  nome: string;
  usuario: string;
  seguidores: number;
  engajamento: number;
  nicho: string;
  localizacao: string;
  foto?: string;
  status: 'ativo' | 'inativo' | 'pendente';
}

interface PageProps {
  params: Promise<{
    token: string;
    visibility: string;
  }>;
}

interface SharedListData {
  lista: Lista;
  itens: any[];
  compartilhamento: {
    id: string;
    token: string;
    tipoAcesso: 'publico' | 'link';
    permissao: 'visualizar' | 'comentar';
    criadoPor: string;
    criadoPorNome: string;
    configuracoes: {
      permitirDownload: boolean;
      permitirComentarios: boolean;
      mostrarEstatisticas: boolean;
      senhaProtegido?: string;
    };
    estatisticas: {
      totalVisualizacoes: number;
    };
  };
}

export default function SharedListPage({ params }: PageProps) {
  const router = useRouter();
  const [token, setToken] = useState<string | null>(null);
  const [visibility, setVisibility] = useState<string | null>(null);
  const [sharedData, setSharedData] = useState<SharedListData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [passwordRequired, setPasswordRequired] = useState(false);
  const [password, setPassword] = useState('');

  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setToken(resolvedParams.token);
      setVisibility(resolvedParams.visibility);
    };
    resolveParams();
  }, [params]);

  // Estado da aba ativa
  const [activeTab, setActiveTab] = useState<'perfis' | 'agregados'>('perfis');

  // Carregar dados compartilhados
  useEffect(() => {
    if (token && visibility) {
      loadSharedData();
    }
  }, [token, visibility]);

  const loadSharedData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/shared/${token}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          setPasswordRequired(true);
          setIsLoading(false);
          return;
        }
        throw new Error(`Erro HTTP: ${response.status}`);
      }

      const data = await response.json();
      setSharedData(data);
      
      // Registrar visualização
      if (data.compartilhamento.configuracoes.mostrarEstatisticas) {
        await registrarVisualizacao();
      }

    } catch (error) {
      console.error('❌ Erro ao carregar dados compartilhados:', error);
      setError('Erro ao carregar lista compartilhada');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordSubmit = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/shared/${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      if (!response.ok) {
        toast.error('Senha incorreta');
        return;
      }

      const data = await response.json();
      setSharedData(data);
      setPasswordRequired(false);
      
      // Registrar visualização
      await registrarVisualizacao();

    } catch (error) {
      console.error('❌ Erro ao verificar senha:', error);
      toast.error('Erro ao verificar senha');
    } finally {
      setIsLoading(false);
    }
  };

  const registrarVisualizacao = async () => {
    try {
      await fetch(`/api/shared/${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'view' }),
      });
    } catch (error) {
      console.error('Erro ao registrar visualização:', error);
    }
  };

  const handleDownload = async () => {
    if (!sharedData?.compartilhamento.configuracoes.permitirDownload) {
      toast.error('Download não permitido para esta lista');
      return;
    }

    try {
      // TODO: Implementar download da lista
      toast.success('Download iniciado!');
    } catch (error) {
      console.error('Erro ao fazer download:', error);
      toast.error('Erro ao fazer download');
    }
  };

  const handleShareLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast.success('Link copiado para clipboard!');
    } catch (error) {
      console.error('Erro ao copiar link:', error);
      toast.error('Erro ao copiar link');
    }
  };

  // Funções de utilidade
  const getTipoObjetoIcon = (tipo: string) => {
    switch (tipo) {
      case 'influenciadores': return <Users className="h-4 w-4" />;
      case 'marcas': return <Tag className="h-4 w-4" />;
      case 'campanhas': return <Calendar className="h-4 w-4" />;
      case 'conteúdo': return <ImageIcon className="h-4 w-4" />;
      default: return <List className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatNumber = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  // Usando estrutura centralizada
  const { dataTableProps } = useDataTable<InfluencerType>({
    enableColumnOrdering: false,
    enableRowSelection: false,
    pageSize: 20
  });

  // Mapear itens para formato esperado
  const mappedItems = useMemo(() => {
    if (!sharedData?.itens) return [];
    
    return sharedData.itens.map((item: any) => ({
      id: item.itemId || item.id,
      nome: item.itemData?.nome || `Influenciador ${item.itemId}`,
      usuario: `@${item.itemData?.nome?.toLowerCase().replace(' ', '') || 'usuario'}`,
      seguidores: Math.floor(Math.random() * 1000000) + 10000, // Mock até termos dados reais
      engajamento: Math.floor(Math.random() * 10) + 1,
      nicho: 'Fashion', // Mock
      localizacao: 'São Paulo, SP',
      foto: item.itemData?.foto,
      status: 'ativo' as const,
      // Adicionar campos necessários para a DataTable
      listItemId: item.id,
      dataAdicao: item.dataAdicao,
      posicao: item.posicao
    }));
  }, [sharedData?.itens]);

  // Colunas usando helper centralizado (apenas visualização)
  const influencerColumns = useMemo(() => createInfluencerColumns(
    {
      onView: (influencer) => toast.success(`Visualizando ${influencer.nome}`)
    },
    {
      showPseudonimo: true,
      showOrigem: true,
      isListView: true
    }
  ), []);

  // Tela de senha
  if (passwordRequired) {
    return (
      <div className="min-h-screen bg-muted/30 flex items-center justify-center p-6">
        <Card className="max-w-md w-full">
          <CardContent className="p-6">
            <div className="text-center mb-6">
              <Lock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold mb-2">Lista Protegida</h2>
              <p className="text-muted-foreground">
                Esta lista é protegida por senha. Digite a senha para continuar.
              </p>
            </div>
            
            <div className="space-y-4">
              <Input
                type="password"
                placeholder="Digite a senha"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handlePasswordSubmit()}
              />
              <Button 
                onClick={handlePasswordSubmit}
                className="w-full"
                disabled={isLoading || !password}
              >
                {isLoading ? 'Verificando...' : 'Acessar Lista'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Loading
  if (isLoading) {
    return <Loader isLoading={true} message="" showLogo={true} />;
  }

  // Error
  if (error || !sharedData) {
    return (
      <div className="min-h-screen bg-muted/30 flex items-center justify-center p-6">
        <Card className="max-w-md w-full">
          <CardContent className="p-6 text-center">
            <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h2 className="text-xl font-semibold mb-2">Erro ao Carregar</h2>
            <p className="text-muted-foreground mb-4">
              {error || 'Lista compartilhada não encontrada ou expirada'}
            </p>
            <Button onClick={() => router.push('/')}>
              Voltar ao Início
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { lista, compartilhamento } = sharedData;

  return (
    <div className="flex bg-muted/30 h-screen">
      {/* Sidebar com detalhes da lista */}
      <div className="w-96 border-r bg-background">
        <div className="p-6 space-y-6">
          {/* Header com botão voltar */}
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push('/')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold">Lista Compartilhada</h1>
              <p className="text-sm text-muted-foreground">
                Compartilhado por {compartilhamento.criadoPorNome}
              </p>
            </div>
          </div>

          {/* Perfil da Lista */}
          <Card>
            <CardContent className="p-6">
              <div className="text-center space-y-4">
                {/* Avatar da lista */}
                <div className="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-[#ec003f] to-[#9810fa] flex items-center justify-center">
                  {lista.tipoObjeto === 'influenciadores' && <Users className="h-8 w-8 text-white" />}
                  {lista.tipoObjeto === 'marcas' && <Tag className="h-8 w-8 text-white" />}
                  {lista.tipoObjeto === 'campanhas' && <Calendar className="h-8 w-8 text-white" />}
                  {lista.tipoObjeto === 'conteúdo' && <ImageIcon className="h-8 w-8 text-white" />}
                  {!['influenciadores', 'marcas', 'campanhas', 'conteúdo'].includes(lista.tipoObjeto) && <List className="h-8 w-8 text-white" />}
                </div>

                {/* Nome da lista */}
                <div>
                  <h2 className="text-xl text-gray-700 font-bold dark:text-white">{lista.nome}</h2>
                  {lista.descricao && (
                    <p className="text-sm text-muted-foreground mt-2">{lista.descricao}</p>
                  )}
                </div>

                {/* Quantidade na lista */}
                <div>
                  <div className="text-3xl font-bold text-[#ec003f]">{mappedItems.length}</div>
                  <div className="text-sm text-muted-foreground">
                    {lista.tipoObjeto} na lista
                  </div>
                </div>

                {/* Tipo da lista */}
                <div>
                  <Badge className={lista.tipoLista === 'estática' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}>
                    Lista {lista.tipoLista}
                  </Badge>
                </div>

                {/* Badges de status */}
                <div className="flex justify-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {compartilhamento.tipoAcesso === 'publico' ? 'Público' : 'Por Link'}
                  </Badge>
                  
                  {compartilhamento.configuracoes.mostrarEstatisticas && (
                    <Badge variant="secondary">
                      {compartilhamento.estatisticas.totalVisualizacoes} visualizações
                    </Badge>
                  )}
                </div>

                {/* Ícones de ação */}
                <div className="flex justify-center gap-4 pt-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-10 w-10 rounded-full hover:bg-muted"
                    onClick={handleShareLink}
                    title="Copiar link"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                  
                  {compartilhamento.configuracoes.permitirDownload && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-10 w-10 rounded-full hover:bg-muted"
                      onClick={handleDownload}
                      title="Download"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tags da lista */}
          {lista.tags && lista.tags.length > 0 && (
            <Card>
              <CardContent className="p-4">
                <h3 className="text-sm font-medium mb-2">Tags</h3>
                <div className="flex flex-wrap gap-1">
                  {lista.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Critérios da lista dinâmica */}
          {lista.tipoLista === 'dinâmica' && lista.criterios && lista.criterios.length > 0 && (
            <Card>
              <CardContent className="p-4">
                <h3 className="text-sm font-medium mb-2">Critérios</h3>
                <div className="space-y-2">
                  {lista.criterios.map((criterio, index) => (
                    <div key={index} className="text-xs">
                      <Badge variant="outline" className="text-xs">
                        {criterio.campo} {criterio.operador} {criterio.valor}
                      </Badge>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Lista atualizada automaticamente
                </p>
              </CardContent>
            </Card>
          )}

          {/* Informações de criação */}
          <Card>
            <CardContent className="p-4">
              <h3 className="text-sm font-medium mb-2">Informações</h3>
              <div className="space-y-2 text-xs text-muted-foreground">
                <div>Criada em: {formatDate(lista.createdAt)}</div>
                <div>Atualizada em: {formatDate(lista.updatedAt)}</div>
                <div>Criador: {lista.criadoPorNome}</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 bg-muted/30 overflow-hidden">
        <div className="h-full p-6 space-y-6 overflow-y-auto">
          {/* Navegação por Abas */}
          <div className="border-b border-border">
            <div className="flex space-x-8">
              <button
                onClick={() => setActiveTab('perfis')}
                className={cn(
                  "pb-3 px-1 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'perfis'
                    ? "border-[#ec003f] text-[#ec003f]"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                Perfis
                <span className="ml-2 bg-muted text-muted-foreground px-2 py-1 rounded-full text-xs">
                  {mappedItems.length}
                </span>
              </button>
              
              <button
                onClick={() => setActiveTab('agregados')}
                className={cn(
                  "pb-3 px-1 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'agregados'
                    ? "border-[#ec003f] text-[#ec003f]"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                Dados agregados
              </button>
            </div>
          </div>

          {/* Conteúdo das Abas */}
          {activeTab === 'perfis' && (
            <>
              {/* DataTable de Perfis */}
              {mappedItems.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    {getTipoObjetoIcon(lista.tipoObjeto)}
                    <h3 className="text-lg font-medium mt-4">Lista vazia</h3>
                    <p className="text-muted-foreground">
                      Esta lista ainda não possui itens.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="bg-white dark:bg-[#0d0814c2] dark:border-[#1c1627] rounded-lg border">
                  <DataTable
                    columns={influencerColumns}
                    data={mappedItems as InfluencerType[]}
                    searchKey="nome"
                    searchPlaceholder={`Buscar ${lista.tipoObjeto}...`}
                    className="w-full"
                    {...dataTableProps}
                  />
                </div>
              )}
            </>
          )}

          {/* Aba Dados Agregados */}
          {activeTab === 'agregados' && (
            <div className="space-y-6">
              {compartilhamento.configuracoes.mostrarEstatisticas ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-[#ec003f] mb-2">
                        {compartilhamento.estatisticas.totalVisualizacoes}
                      </div>
                      <div className="text-sm text-muted-foreground">Total de Visualizações</div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-[#9810fa] mb-2">
                        {mappedItems.length}
                      </div>
                      <div className="text-sm text-muted-foreground">Itens na Lista</div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-green-600 mb-2">
                        {compartilhamento.tipoAcesso === 'publico' ? 'Público' : 'Privado'}
                      </div>
                      <div className="text-sm text-muted-foreground">Tipo de Acesso</div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">Estatísticas não disponíveis</h3>
                  <p className="text-muted-foreground">
                    O criador da lista optou por não mostrar estatísticas.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 