import { NextRequest, NextResponse } from 'next/server';
import { ListSharingService } from '@/lib/list-sharing-service';
import { ListService } from '@/services/list-service';

// GET - Listar compartilhamentos de uma lista
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: listaId } = await params;
    console.log('🔍 API: Buscando compartilhamentos para lista:', listaId);
    
    const [colaboradores, convitesPendentes] = await Promise.all([
      ListSharingService.listarColaboradores(listaId),
      ListSharingService.listarConvitesPendentes(listaId)
    ]);

    console.log('📊 API: Colaboradores encontrados:', colaboradores.length);
    console.log('📨 API: Convites pendentes encontrados:', convitesPendentes.length);

    // Serializar datas para JSON
    const colaboradoresSerializados = colaboradores.map(colaborador => ({
      ...colaborador,
      convidadoEm: colaborador.convidadoEm.toISOString(),
      aceitouEm: colaborador.aceitouEm.toISOString()
    }));

    const convitesSerializados = convitesPendentes.map(convite => ({
      ...convite,
      createdAt: convite.createdAt.toISOString(),
      expiresAt: convite.expiresAt.toISOString()
    }));

    return NextResponse.json({
      success: true,
      colaboradores: colaboradoresSerializados,
      convitesPendentes: convitesSerializados
    });

  } catch (error) {
    console.error('❌ API: Erro ao buscar compartilhamentos:', error);
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

// POST - Criar compartilhamento (convite ou link público)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: listaId } = await params;
    const dados = await request.json();
    
    console.log('📝 API: Criando compartilhamento:', dados);

    // Verificar se a lista existe e obter informações
    const lista = await ListService.buscarListaPorId(listaId, dados.userId);
    if (!lista) {
      return NextResponse.json(
        { error: 'Lista não encontrada' },
        { status: 404 }
      );
    }

    // Verificar se é solicitação de link público
    if (dados.tipoCompartilhamento === 'link_publico') {
      const resultado = await ListSharingService.gerarLinkPublico({
        listaId,
        listaNome: lista.nome,
        criadoPor: lista.criadoPor,
        criadoPorNome: lista.criadoPorNome,
        configuracoes: dados.configuracoes
      });

      return NextResponse.json({
        success: true,
        token: resultado.token,
        linkCompartilhamento: resultado.linkCompartilhamento,
        linkCompleto: `${request.nextUrl.origin}${resultado.linkCompartilhamento}`,
        message: 'Link público gerado com sucesso'
      });
    }

    // Criar convite para usuário específico
    if (dados.tipoCompartilhamento === 'convite' && dados.emailsUsuarios?.length > 0) {
      const resultados = [];
      
      for (const email of dados.emailsUsuarios) {
        const resultado = await ListSharingService.criarConvite({
          listaId,
          listaNome: lista.nome,
          emailConvidado: email,
          permissao: dados.permissao,
          mensagem: dados.mensagem
        }, {
          userId: dados.userId,
          nome: dados.userNome || 'Usuário',
          email: dados.userEmail || '<EMAIL>'
        });
        
        resultados.push(resultado);
      }

      return NextResponse.json({
        success: true,
        convites: resultados,
        message: 'Convites enviados com sucesso'
      });
    }

    return NextResponse.json(
      { error: 'Tipo de compartilhamento inválido' },
      { status: 400 }
    );

  } catch (error) {
    console.error('❌ API: Erro ao criar compartilhamento:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Desativar compartilhamento público
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: listaId } = await params;
    const { userId } = await request.json();
    
    await ListSharingService.desativarCompartilhamento(listaId, userId);
    
    return NextResponse.json({
      success: true,
      message: 'Compartilhamento desativado com sucesso'
    });

  } catch (error) {
    console.error('❌ API: Erro ao desativar compartilhamento:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 