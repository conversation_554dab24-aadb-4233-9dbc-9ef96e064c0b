import { NextRequest, NextResponse } from 'next/server';
import { ListService } from '@/services/list-service';
import { withAddItemToListValidation } from '@/lib/middleware/lists-middleware';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { AdicionarItemListaData } from '@/types/list';

/**
 * 📝 API ROUTES PARA ITENS DAS LISTAS
 * Endpoints para operações com itens dentro das listas
 */

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * GET /api/lists/[id]/items - Buscar itens de uma lista
 */
export async function GET(req: NextRequest, { params }: RouteParams) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      const resolvedParams = await params;
      
      try {
        const url = new URL(req.url);
        const limite = parseInt(url.searchParams.get('limite') || '50');
        const offset = parseInt(url.searchParams.get('offset') || '0');

        console.log('[API_LIST_ITEMS_GET] Parâmetros da requisição:', {
          listaId: resolvedParams.id,
          userId,
          limite,
          offset
        });

        const resultado = await ListService.buscarItensLista(
          resolvedParams.id,
          userId,
          limite,
          offset
        );

        console.log('[API_LIST_ITEMS_GET] Resultado do service:', {
          itensCount: resultado.itens?.length || 0,
          total: resultado.total
        });

        return NextResponse.json(resultado);

      } catch (error: any) {
        console.error('[API_LIST_ITEMS_GET]', { userId, listaId: resolvedParams.id, error });
        
        if (error?.message?.includes('não encontrada')) {
          return NextResponse.json(
            { error: 'Lista não encontrada' },
            { status: 404 }
          );
        }

        if (error?.message?.includes('permissão')) {
          return NextResponse.json(
            { error: 'Sem permissão para acessar esta lista' },
            { status: 403 }
          );
        }

        return NextResponse.json(
          { error: 'Erro interno ao buscar itens da lista' },
          { status: 500 }
        );
      }
    }
  )(req);
}

/**
 * POST /api/lists/[id]/items - Adicionar item à lista
 */
export async function POST(req: NextRequest, { params }: RouteParams) {
  return withAddItemToListValidation(
    async (req: NextRequest, userId: string, context: any, listaId: string, data: AdicionarItemListaData) => {
      try {
        const itemAdicionado = await ListService.adicionarItemLista(data, userId);

        return NextResponse.json(itemAdicionado, { status: 201 });

      } catch (error: any) {
        console.error('[API_LIST_ITEMS_POST]', { userId, listaId, data, error });
        
        if (error?.message?.includes('não encontrada')) {
          return NextResponse.json(
            { error: 'Lista não encontrada' },
            { status: 404 }
          );
        }

        if (error?.message?.includes('permissão')) {
          return NextResponse.json(
            { error: 'Sem permissão para adicionar itens nesta lista' },
            { status: 403 }
          );
        }

        if (error?.message?.includes('já está presente')) {
          return NextResponse.json(
            { error: 'Item já está na lista' },
            { status: 409 }
          );
        }

        return NextResponse.json(
          { error: 'Erro interno ao adicionar item à lista' },
          { status: 500 }
        );
      }
    }
  )(req);
} 